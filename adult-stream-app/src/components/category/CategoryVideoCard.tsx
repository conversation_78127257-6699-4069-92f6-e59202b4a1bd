'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Video } from '@/lib/types';
import { formatDuration, formatViews, formatTimeAgo } from '@/lib/utils';

interface CategoryVideoCardProps {
  video: Video;
  className?: string;
  onClick?: () => void;
}

export function CategoryVideoCard({ video, className, onClick }: CategoryVideoCardProps) {
  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  const cardContent = (
    <div className={`group flex flex-col ${className || ''}`}>
      {/* Video Thumbnail Container */}
      <div className="relative aspect-video overflow-hidden rounded-lg shadow-lg transition-all hover:scale-105">
      {/* Optimized Background Image */}
      <div className="relative w-full h-full">
        <Image
          src={video.thumbnail}
          alt={video.title}
          fill
          className="object-cover"
          sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, (max-width: 1280px) 20vw, 16vw"
          priority={false}
        />
      </div>

      {/* Play Button Overlay */}
      <div className="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 transition-opacity group-hover:opacity-100">
        <svg className="h-12 w-12 text-white" fill="currentColor" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
          <path d="M240,128a15.71,15.71,0,0,1-7.6,13.51L88.32,223.25a16,16,0,0,1-24.32-13.51V46.26a16,16,0,0,1,24.32-13.51L232.4,114.49A15.71,15.71,0,0,1,240,128Z" />
        </svg>
      </div>

      {/* Duration Badge */}
      <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs font-medium px-2 py-1 rounded">
        {formatDuration(video.duration)}
      </div>

      {/* Hover Actions */}
      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <div className="flex gap-1">
          {/* Favorite Button */}
          <button
            className="bg-black/60 hover:bg-black/80 text-white p-1.5 rounded-full transition-colors"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              // Handle favorite action
            }}
            aria-label="Add to favorites"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
            </svg>
          </button>

          {/* Watch Later Button */}
          <button
            className="bg-black/60 hover:bg-black/80 text-white p-1.5 rounded-full transition-colors"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              // Handle watch later action
            }}
            aria-label="Watch later"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          </button>
        </div>
      </div>
      </div>

      {/* Video Metadata Section - Below Thumbnail */}
      <div className="mt-3 space-y-2 px-1">
        {/* Video Title */}
        <h3 className="text-sm font-semibold text-white line-clamp-2 leading-tight hover:text-gray-200 transition-colors cursor-pointer">
          {video.title}
        </h3>

        {/* Video Stats */}
        <div className="flex items-center text-xs text-gray-400 space-x-1">
          <span className="hover:text-gray-300 transition-colors">{formatViews(video.views)} views</span>
          <span className="text-gray-600">•</span>
          <span className="hover:text-gray-300 transition-colors">{formatTimeAgo(video.uploadedAt)}</span>
        </div>

        {/* Quality Indicators (Mobile-friendly) */}
        {(video.isHD || video.isPremium) && (
          <div className="flex gap-1 mt-1">
            {video.isHD && (
              <span className="bg-[#E92933] text-white text-xs font-bold px-1.5 py-0.5 rounded">
                HD
              </span>
            )}
            {video.isPremium && (
              <span className="bg-yellow-500 text-black text-xs font-bold px-1.5 py-0.5 rounded">
                PREMIUM
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );

  if (onClick) {
    return (
      <div onClick={handleClick} className="cursor-pointer">
        {cardContent}
      </div>
    );
  }

  return (
    <Link href={`/video/${video.id}`} className="block">
      {cardContent}
    </Link>
  );
}
