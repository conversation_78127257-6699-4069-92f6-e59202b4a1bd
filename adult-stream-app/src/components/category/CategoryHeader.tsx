'use client';

import React, { useState, lazy, Suspense } from 'react';
import Link from 'next/link';

// Lazy load mobile menu for better performance
const MobileMenu = lazy(() => import('./MobileMenu').then(module => ({ default: module.MobileMenu })));

interface CategoryHeaderProps {
  className?: string;
}

export function CategoryHeader({ className }: CategoryHeaderProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <>
    <header className={`flex items-center justify-between whitespace-nowrap border-b border-solid border-border px-4 sm:px-6 lg:px-10 py-3 ${className || ''}`}>
      {/* Left side - Logo and Navigation */}
      <div className="flex items-center gap-4 md:gap-8">
        {/* Logo */}
        <Link href="/" className="flex items-center gap-2 text-white">
          <svg className="h-6 w-6 text-[#E92933]" fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
            <path
              clipRule="evenodd"
              d="M24 4H42V17.3333V30.6667H24V44H6V30.6667V17.3333H24V4Z"
              fill="currentColor"
              fillRule="evenodd"
            />
          </svg>
          <h2 className="text-white text-xl font-bold leading-tight tracking-[-0.015em]">Streamr</h2>
        </Link>

        {/* Navigation - Hidden on mobile */}
        <nav className="hidden md:flex items-center gap-6">
          <Link
            className="text-gray-300 hover:text-white text-sm font-medium leading-normal transition-colors"
            href="/"
          >
            Home
          </Link>
          <Link
            className="text-gray-300 hover:text-white text-sm font-medium leading-normal transition-colors"
            href="/categories"
          >
            Categories
          </Link>
          <Link
            className="text-gray-300 hover:text-white text-sm font-medium leading-normal transition-colors"
            href="#"
          >
            Models
          </Link>
          <Link
            className="text-gray-300 hover:text-white text-sm font-medium leading-normal transition-colors"
            href="#"
          >
            Premium
          </Link>
        </nav>
      </div>

      {/* Right side - Search, Bookmarks, User */}
      <div className="flex flex-1 items-center justify-end gap-2 sm:gap-4">
        {/* Search Bar */}
        <label className="relative flex-1 max-w-xs">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <svg className="h-5 w-5 text-[#C89295]" fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
              <path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z" />
            </svg>
          </div>
          <input
            className="form-input block w-full rounded-lg border-none bg-[#391D1F] py-2.5 pl-10 pr-3 text-white placeholder:text-[#C89295] focus:outline-none focus:ring-2 focus:ring-[#E92933] focus:ring-offset-2 focus:ring-offset-[#1A0B0C] sm:text-sm"
            placeholder="Search videos..."
            type="text"
          />
        </label>

        {/* Bookmarks Button */}
        <button
          className="flex items-center justify-center rounded-lg p-2.5 text-white transition-colors hover:bg-[#391D1F] focus:outline-none focus:ring-2 focus:ring-[#E92933] focus:ring-offset-2 focus:ring-offset-[#1A0B0C]"
          aria-label="Bookmarks"
        >
          <svg fill="currentColor" height="20px" viewBox="0 0 256 256" width="20px" xmlns="http://www.w3.org/2000/svg">
            <path d="M184,32H72A16,16,0,0,0,56,48V224a8,8,0,0,0,12.24,6.78L128,193.43l59.77,37.35A8,8,0,0,0,200,224V48A16,16,0,0,0,184,32Zm0,177.57-51.77-32.35a8,8,0,0,0-8.48,0L72,209.57V48H184Z" />
          </svg>
        </button>

        {/* User Avatar */}
        <div
          className="aspect-square size-10 rounded-full bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: 'url("https://lh3.googleusercontent.com/aida-public/AB6AXuBrgo5NeXaf1PG9RgXgIDK3n5IPlb1GgzT75g1TjaBc0tAUMmUFsRjjTEIXpfsiCEBqkNPgniynMop2KNEbUBljuJlMbP6n_vkbsloyQSZVm0gixenHSGFS5-l8jkYeUaTme6FsAsT9iaNWj5h3M4oO7uBSZxHz2qf44FvvOan7QOnGQql8Gx74w4UCL70LlrKCxkil9dZD1IUd6BKKL5IKtQLbHmK1JY1E9vSfFUkFUsECZPI6qvLEFDBbsU1abUikZViSSqdXcZHx")'
          }}
        />

        {/* Mobile Menu Button */}
        <button
          className="md:hidden text-gray-300 hover:text-white p-2.5"
          aria-label="Open menu"
          onClick={() => setIsMobileMenuOpen(true)}
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M4 6h16M4 12h16m-7 6h7" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" />
          </svg>
        </button>
      </div>
    </header>

    {/* Mobile Menu */}
    {isMobileMenuOpen && (
      <Suspense fallback={null}>
        <MobileMenu
          isOpen={isMobileMenuOpen}
          onClose={() => setIsMobileMenuOpen(false)}
        />
      </Suspense>
    )}
    </>
  );
}
