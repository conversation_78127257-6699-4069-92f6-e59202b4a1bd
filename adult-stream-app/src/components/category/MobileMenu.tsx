'use client';

import React, { useState } from 'react';
import Link from 'next/link';

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
}

export function MobileMenu({ isOpen, onClose }: MobileMenuProps) {
  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 z-40 md:hidden"
        onClick={onClose}
      />

      {/* Menu Panel */}
      <div className="fixed top-0 right-0 h-full w-64 bg-background-primary border-l border-border z-50 md:hidden">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-border">
            <h3 className="text-white font-semibold">Menu</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white p-2"
              aria-label="Close menu"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Navigation Links */}
          <nav className="flex-1 p-4">
            <div className="space-y-4">
              <Link
                href="/"
                className="block text-gray-300 hover:text-white py-2 transition-colors"
                onClick={onClose}
              >
                Home
              </Link>
              <Link
                href="/categories"
                className="block text-gray-300 hover:text-white py-2 transition-colors"
                onClick={onClose}
              >
                Categories
              </Link>
              <Link
                href="#"
                className="block text-gray-300 hover:text-white py-2 transition-colors"
                onClick={onClose}
              >
                Models
              </Link>
              <Link
                href="#"
                className="block text-gray-300 hover:text-white py-2 transition-colors"
                onClick={onClose}
              >
                Premium
              </Link>

              {/* Divider */}
              <div className="border-t border-border my-4" />

              <Link
                href="#"
                className="block text-gray-300 hover:text-white py-2 transition-colors"
                onClick={onClose}
              >
                Favorites
              </Link>
              <Link
                href="#"
                className="block text-gray-300 hover:text-white py-2 transition-colors"
                onClick={onClose}
              >
                Watch Later
              </Link>
              <Link
                href="#"
                className="block text-gray-300 hover:text-white py-2 transition-colors"
                onClick={onClose}
              >
                History
              </Link>
            </div>
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-border">
            <button className="w-full bg-primary hover:bg-primary-hover text-white font-semibold py-2 px-4 rounded-lg transition-colors">
              Sign In
            </button>
          </div>
        </div>
      </div>
    </>
  );
}
