'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import { cn } from '@/lib/utils';

interface HeaderProps {
  className?: string;
}

export function Header({ className }: HeaderProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navigation = [
    { name: 'Home', href: '/' },
    { name: 'Videos', href: '/videos' },
    { name: 'Categories', href: '/categories' },
    { name: 'Models', href: '/models' },
    { name: 'Live', href: '/live' },
    { name: 'Premium', href: '/premium' },
  ];

  return (
    <header className={cn(
      "flex items-center justify-between whitespace-nowrap border-b border-solid border-border px-6 md:px-10 py-4",
      className
    )}>
      {/* Logo and Navigation */}
      <div className="flex items-center gap-6 md:gap-8">
        {/* Logo */}
        <Link href="/" className="flex items-center gap-2 text-text">
          <div className="size-5 md:size-6 text-primary">
            <svg fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M24 4C25.7818 14.2173 33.7827 22.2182 44 24C33.7827 25.7818 25.7818 33.7827 24 44C22.2182 33.7827 14.2173 25.7818 4 24C14.2173 22.2182 22.2182 14.2173 24 4Z"
                fill="currentColor"
              />
            </svg>
          </div>
          <h1 className="text-text text-xl md:text-2xl font-bold tracking-tight">
            AdultStream
          </h1>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center gap-6">
          {navigation.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className="text-text hover:text-primary text-sm font-semibold leading-normal transition-colors"
            >
              {item.name}
            </Link>
          ))}
        </nav>
      </div>

      {/* Right Side Actions */}
      <div className="flex flex-1 justify-end items-center gap-3 md:gap-4">
        {/* Search Bar - Hidden on mobile */}
        <label className="relative flex-1 max-w-xs hidden sm:block">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <svg className="h-5 w-5 text-[#C89295]" fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
              <path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z" />
            </svg>
          </div>
          <input
            className="form-input block w-full rounded-lg border-none bg-[#391D1F] py-2.5 pl-10 pr-3 text-white placeholder:text-[#C89295] focus:outline-none focus:ring-2 focus:ring-[#E92933] focus:ring-offset-2 focus:ring-offset-[#1A0B0C] sm:text-sm"
            placeholder="Search videos..."
            type="text"
          />
        </label>

        {/* Notifications */}
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center justify-center rounded-lg h-10 w-10 bg-background-input hover:bg-background-hover text-text transition-colors"
          aria-label="Notifications"
        >
          <svg
            fill="currentColor"
            height="20px"
            viewBox="0 0 256 256"
            width="20px"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-43.92,16-80a64,64,0,1,1,128,0c0,36.05,8.28,66.73,16,80Z" />
          </svg>
        </Button>

        {/* User Avatar */}
        <div
          className="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10 border-2 border-primary cursor-pointer"
          style={{
            backgroundImage: `url("https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face")`,
          }}
        />

        {/* Mobile Menu Button */}
        <Button
          variant="ghost"
          size="sm"
          className="md:hidden text-text hover:text-primary p-2"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          aria-label="Toggle mobile menu"
        >
          <svg
            fill="currentColor"
            height="24px"
            viewBox="0 0 256 256"
            width="24px"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M224,128a8,8,0,0,1-8,8H40a8,8,0,0,1,0-16H216A8,8,0,0,1,224,128ZM40,88H216a8,8,0,0,0,0-16H40a8,8,0,0,0,0,16ZM40,184H216a8,8,0,0,0,0-16H40a8,8,0,0,0,0,16Z" />
          </svg>
        </Button>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="absolute top-full left-0 right-0 bg-background-card border-b border-border md:hidden z-50">
          <div className="px-6 py-4 space-y-4">
            {/* Mobile Search */}
            <label className="relative">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <svg className="h-5 w-5 text-[#C89295]" fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
                  <path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z" />
                </svg>
              </div>
              <input
                className="form-input block w-full rounded-lg border-none bg-[#391D1F] py-2.5 pl-10 pr-3 text-white placeholder:text-[#C89295] focus:outline-none focus:ring-2 focus:ring-[#E92933] focus:ring-offset-2 focus:ring-offset-[#1A0B0C] sm:text-sm"
                placeholder="Search videos..."
                type="text"
              />
            </label>

            {/* Mobile Navigation */}
            <nav className="space-y-2">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="block text-text hover:text-primary text-sm font-semibold leading-normal transition-colors py-2"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
            </nav>
          </div>
        </div>
      )}
    </header>
  );
}
